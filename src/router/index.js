import { createRouter, createWebHashHistory } from 'vue-router'
import Store from '@/store/index.js'
import AppApi from '@/api/sys/app.js'

const _routes = [
  // 使用管理页面布局
  {
    path: '/',
    component: () => import('@/layouts/AdminTabLayout.vue'),
    children: [
      // 主页
      {
        name: 'admin.home',
        path: '/',
        component: () => import('@/views/Home.vue'),
        meta: {
          title: '主页',
          anon: false
        }
      },
      {
        name: 'llm',
        path: '/llm',
        component: () => import('@/views/llm/Chat.vue'),
        meta: {
          title: '大模型对话',
          keepAlive: true
        }
      },
      // 我的
      {
        name: 'admin.me-setting',
        path: '/me/setting',
        component: () => import('@/views/sys/me/Setting.vue'),
        meta: {
          title: '个人中心',
          anon: false,
          keepAlive: true
        }
      }, {
        name: 'admin.me-message',
        path: '/me/message',
        component: () => import('@/views/sys/me/Message.vue'),
        meta: {
          title: '消息中心',
          anon: false
        }
      },
      // 文件管理
      {
        name: 'admin.media',
        path: '/media',
        component: () => import('@/views/media/Record.vue'),
        meta: {
          title: '文件管理',
          anon: false
        }
      },
      // 安全
      {
        name: 'admin.sec.online',
        path: '/sec/online',
        component: () => import('@/views/sec/Online.vue'),
        meta: {
          title: '在线管理',
          anon: false
        }
      },
      // 角色管理
      {
        name: 'admin.sec.role.index',
        path: '/sec/role',
        component: () => import('@/views/sec/Role.vue'),
        meta: {
          title: '角色管理',
          anon: false
        }
      }, {
        name: 'admin.sec.role.edit',
        path: '/sec/role/edit',
        component: () => import('@/views/sec/RoleEditor.vue'),
        meta: {
          title: '角色编辑',
          anon: false
        }
      }, {
        name: 'admin.sec.role.authorization',
        path: '/sec/role/authorization',
        component: () => import('@/views/sec/RoleAuthorization.vue'),
        meta: {
          title: '角色授权',
          anon: false
        }
      }, {
        name: 'admin.sec.role.appointment',
        path: '/sec/role/appointment',
        component: () => import('@/views/sec/RoleAppointment.vue'),
        meta: {
          title: '人员任命',
          anon: false
        }
      },
      // 活跃统计
      {
        name: 'admin.sys.stat',
        path: '/sys/stat',
        component: () => import('@/views/sys/Stat.vue'),
        meta: {
          title: '活跃统计',
          anon: false
        }
      },
      // 通讯录
      {
        name: 'admin.sys.contact',
        path: '/sys/contact',
        component: () => import('@/views/sys/Contact.vue'),
        meta: {
          title: '通讯录',
          anon: false
        }
      },
      // 用户编辑
      {
        name: 'admin.sys.user.edit',
        path: '/sys/user/edit',
        component: () => import('@/views/sys/UserEditor.vue'),
        meta: {
          title: '用户编辑',
          anon: false
        }
      },
      // 作业管理
      {
        name: 'admin.sys.job',
        path: '/sys/job',
        component: () => import('@/views/sys/Jobs.vue'),
        meta: {
          title: '作业管理',
          anon: false,
          keepAlive: true
        }
      },
      // 字典管理
      {
        name: 'admin.sys.dictionary',
        path: '/sys/dictionary',
        component: () => import('@/views/sys/Dictionary.vue'),
        meta: {
          title: '字典管理',
          anon: false
        }
      }, {
        name: 'admin.sys.dictionary.edit',
        path: '/sys/dictionary/edit',
        component: () => import('@/views/sys/DictionaryEditor.vue'),
        meta: {
          title: '字典项目编辑',
          anon: false
        }
      },
      // 短信管理
      {
        name: 'admin.sys.sms.outbox',
        path: '/sys/sms/outbox',
        component: () => import('@/views/sys/sms/Outbox.vue'),
        meta: {
          title: '发件箱',
          anon: false
        }
      },
      // 信息日志查询
      {
        name: 'admin.sys.log.info',
        path: '/sys/log/info',
        component: () => import('@/views/sys/log/Info.vue'),
        meta: {
          title: '信息日志',
          anon: false
        }
      },
      // 错误日志查询
      {
        name: 'admin.sys.log.error',
        path: '/sys/log/error',
        component: () => import('@/views/sys/log/Error.vue'),
        meta: {
          title: '错误日志',
          anon: false
        }
      },
      // 大模型
      {
        name: 'admin.sys.llm.model.index',
        path: '/sys/llm/model',
        component: () => import('@/views/sys/llm/Model.vue'),
        meta: {
          title: '大模型管理',
          anon: false
        }
      }, {
        name: 'admin.sys.llm.model.edit',
        path: '/sys/llm/model/edit',
        component: () => import('@/views/sys/llm/ModelEditor.vue'),
        meta: {
          title: '大模型编辑',
          anon: false
        }
      },
      // 其它
      {
        path: '/:pathMatch(.*)',
        component: () => import('@/views/exception/404.vue'),
        meta: {
          title: '404',
          anon: true
        }
      }
    ]
  },
  // 使用登录页面布局
  {
    path: '/',
    component: () => import('@/layouts/LoginLayout.vue'),
    children: [
      {
        name: 'login.index',
        path: '/sec/login',
        component: () => import('@/views/sec/Login.vue'),
        meta: {
          anon: true
        }
      }
    ]
  }
]

export const router = createRouter({
  history: createWebHashHistory(),
  routes: _routes
})

router.beforeEach((to, from, next) => {
  if (to.meta.anon) { // 支持匿名访问则导航
    next()
  } else if (Store.state.credential.authenticated) { // 已登录则鉴权
    const _promise = Store.state.assets.pages === null
      ? AppApi.getPages({
        showLoading: false,
        toast: {
          success: false
        }
      })
      : Promise.resolve()

    _promise.finally(() => {
      next()
    })
  } else { // 未登录则重定向
    next({
      path: '/sec/login',
      query: {
        redirect: to.fullPath
      }
    })
  }
})

router.afterEach((to, from) => {
})

export const unreliablyUpdateRoutes = pages => {
  const _routes = router.getRoutes()

  // 更新路由
  pages
    .filter(i => typeof i.path === 'string')
    .forEach(i => {
      _routes
        .filter(j => i.path === j.path)
        .forEach(j => {
          j.meta.title = i.title || j.meta.title
          j.meta.anon = i.anon == null ? i.anon : j.meta.anon
        })
    })
}

// 搜索需要缓存的路由
const findKeepAlive = routes => {
  let _array = []

  routes.forEach(value => {
    if (value.meta && value.meta.keepAlive) {
      _array.push(value.name)
    }

    // 递归子路由
    if (Array.isArray(value.children)) {
      _array = _array.concat(findKeepAlive(value.children))
    }
  })

  return _array
}

export const keepAliveComponents = findKeepAlive(_routes)
